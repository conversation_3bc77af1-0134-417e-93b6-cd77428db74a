import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { Provider, ProviderQueryParams } from '../types/models';
import { useProviders } from '../hooks/useVCloudDb';
import { formatTimestamp, formatObject } from '../utils/exportUtils';
import { Search, Filter, Download, Plus, RefreshCw, Building } from 'lucide-react';

export const ProvidersPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = useProviders({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<Provider>[] = [
    {
      key: '_id',
      title: 'ID',
      dataIndex: '_id',
      width: 120,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value.substring(0, 8)}...
        </span>
      )
    },
    {
      key: 'name',
      title: '提供商名称',
      dataIndex: 'name',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="font-medium text-gray-900">{value}</span>
      )
    },
    {
      key: 'walletAddress',
      title: '钱包地址',
      dataIndex: 'walletAddress',
      width: 180,
      sortable: true,
      render: (value) => (
        <span className="font-mono text-xs text-blue-600">
          {value.substring(0, 12)}...
        </span>
      )
    },
    {
      key: 'publickey',
      title: '公钥',
      dataIndex: 'publickey',
      width: 150,
      render: (value) => (
        <span className="font-mono text-xs text-green-600">
          {value ? `${value.substring(0, 12)}...` : '-'}
        </span>
      )
    },
    {
      key: 'signAddress',
      title: '签名地址',
      dataIndex: 'signAddress',
      width: 150,
      render: (value) => (
        <span className="font-mono text-xs text-purple-600">
          {value ? `${value.substring(0, 12)}...` : '-'}
        </span>
      )
    },
    {
      key: 'apiHost',
      title: 'API主机',
      dataIndex: 'apiHost',
      width: 180,
      render: (value) => (
        <span className="font-mono text-xs text-green-600">
          {value || '-'}
        </span>
      )
    },
    {
      key: 'category2ID',
      title: '分类映射',
      dataIndex: 'category2ID',
      width: 120,
      render: (value) => {
        const categoryCount = value ? Object.keys(value).length : 0;
        return (
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
            {categoryCount} 个分类
          </span>
        );
      }
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {new Date(value * 1000).toLocaleString()}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'name',
      label: '提供商名称',
      type: 'text',
      placeholder: '输入提供商名称...'
    },
    {
      key: 'walletAddress',
      label: '钱包地址',
      type: 'text',
      placeholder: '输入钱包地址...'
    },
    {
      key: 'publicKey',
      label: '公钥',
      type: 'text',
      placeholder: '输入公钥...'
    },
    {
      key: 'signAddress',
      label: '签名地址',
      type: 'text',
      placeholder: '输入签名地址...'
    },
    {
      key: 'apiHost',
      label: 'API主机',
      type: 'text',
      placeholder: '输入API主机地址...'
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleQuery = (values: Record<string, any>) => {
    const queryParams: Partial<ProviderQueryParams> = {
      offset: 0,
      ...values
    };

    updateParams(queryParams);
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      name: undefined,
      walletAddress: undefined,
      publicKey: undefined,
      signAddress: undefined,
      apiHost: undefined,
      offset: 0
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Building className="h-6 w-6 mr-2 text-blue-600" />
            服务提供商管理
          </h1>
          <p className="text-gray-600 mt-1">管理服务提供商信息和配置</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '显示筛选'}
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'name', title: '提供商名称' },
              { key: 'walletAddress', title: '钱包地址' },
              { key: 'publickey', title: '公钥' },
              { key: 'signAddress', title: '签名地址' },
              { key: 'apiHost', title: 'API主机' },
              { key: 'category2ID', title: '分类映射', render: (value) => formatObject(value) },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="providers"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <QueryForm
            fields={queryFields}
            onSubmit={handleQuery}
            onReset={handleResetQuery}
            loading={loading}
            initialValues={params}
          />
        </div>
      )}

      {/* 数据统计 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Building className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-900">
                总提供商数: {total.toLocaleString()}
              </span>
            </div>
            {params.name && (
              <div className="text-sm text-gray-600">
                名称: {params.name}
              </div>
            )}
            {params.walletAddress && (
              <div className="text-sm text-gray-600">
                钱包: {params.walletAddress}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          error={error}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: handlePageChange,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onSort={handleSort}
          sortBy={params.sortBy}
          sortDesc={params.sortDesc}
        />
      </div>
    </div>
  );
};
