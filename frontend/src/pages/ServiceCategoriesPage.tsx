import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { DetailModal, DetailField } from '../components/common/DetailModal';
import { ServiceCategory, ServiceCategoryQueryParams } from '../types/models';
import { useServiceCategories } from '../hooks/useVCloudDb';
import { formatTimestamp, formatObject } from '../utils/exportUtils';
import { Search, Filter, Download, Plus, RefreshCw, Grid3X3 } from 'lucide-react';

export const ServiceCategoriesPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ServiceCategory | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = useServiceCategories({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<ServiceCategory>[] = [
    {
      key: '_id',
      title: 'ID',
      dataIndex: '_id',
      width: 120,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value.substring(0, 8)}...
        </span>
      )
    },
    {
      key: 'name',
      title: '分类名称',
      dataIndex: 'name',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="font-medium text-gray-900">{value}</span>
      )
    },
    {
      key: 'provider',
      title: '服务提供商',
      dataIndex: 'provider',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="text-blue-600 font-medium">{value}</span>
      )
    },
    {
      key: 'description',
      title: '描述',
      dataIndex: 'description',
      width: 200,
      render: (value) => (
        <span className="text-sm text-gray-600 truncate" title={value}>
          {value || '-'}
        </span>
      )
    },
    {
      key: 'apiHost',
      title: 'API主机',
      dataIndex: 'apiHost',
      width: 180,
      render: (value) => (
        <span className="font-mono text-xs text-green-600">
          {value || '-'}
        </span>
      )
    },
    {
      key: 'serviceOptions',
      title: '服务选项',
      dataIndex: 'serviceOptions',
      width: 150,
      render: (value) => {
        const optionCount = value ? Object.keys(value).length : 0;
        return (
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
            {optionCount} 个选项
          </span>
        );
      }
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {new Date(value * 1000).toLocaleString()}
        </span>
      )
    },
    {
      key: 'updatedAt',
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {new Date(value * 1000).toLocaleString()}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'provider',
      label: '服务提供商',
      type: 'text',
      placeholder: '输入服务提供商名称...'
    },
    {
      key: 'name',
      label: '分类名称',
      type: 'text',
      placeholder: '输入分类名称...'
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleQuery = (values: Record<string, any>) => {
    const queryParams: Partial<ServiceCategoryQueryParams> = {
      offset: 0,
      ...values
    };

    updateParams(queryParams);
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      provider: undefined,
      name: undefined,
      offset: 0
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Grid3X3 className="h-6 w-6 mr-2 text-blue-600" />
            服务分类管理
          </h1>
          <p className="text-gray-600 mt-1">管理服务分类和配置选项</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '显示筛选'}
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'name', title: '分类名称' },
              { key: 'provider', title: '服务提供商' },
              { key: 'description', title: '描述' },
              { key: 'apiHost', title: 'API主机' },
              { key: 'serviceOptions', title: '服务选项', render: (value) => formatObject(value) },
              { key: 'name2ID', title: '名称映射', render: (value) => formatObject(value) },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="service_categories"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <QueryForm
            fields={queryFields}
            onSubmit={handleQuery}
            onReset={handleResetQuery}
            loading={loading}
            initialValues={params}
          />
        </div>
      )}

      {/* 数据统计 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Grid3X3 className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-900">
                总分类数: {total.toLocaleString()}
              </span>
            </div>
            {params.provider && (
              <div className="text-sm text-gray-600">
                提供商: {params.provider}
              </div>
            )}
            {params.name && (
              <div className="text-sm text-gray-600">
                分类: {params.name}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          error={error}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: handlePageChange,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onSort={handleSort}
          sortBy={params.sortBy}
          sortDesc={params.sortDesc}
        />
      </div>
    </div>
  );
};
