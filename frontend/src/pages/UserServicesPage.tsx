import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { ExportButton } from '../components/common/ExportButton';
import { DetailModal, DetailField } from '../components/common/DetailModal';
import { UserService, UserServiceQueryParams } from '../types/models';
import { useUserServices } from '../hooks/useVCloudDb';
import { formatTimestamp, formatBoolean } from '../utils/exportUtils';
import { Search, Filter, Download, Plus, RefreshCw, Eye, Server } from 'lucide-react';

export const UserServicesPage: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<Partial<UserServiceQueryParams>>({});
  const [selectedRecord, setSelectedRecord] = useState<UserService | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = useUserServices({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<UserService>[] = [
    {
      key: 'serviceID',
      title: '服务ID',
      dataIndex: 'serviceID',
      width: 200,
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm text-blue-600">{value}</span>
      )
    },
    {
      key: 'address',
      title: '用户地址',
      dataIndex: 'address',
      width: 180,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value ? `${value.slice(0, 8)}...${value.slice(-6)}` : '-'}
        </span>
      )
    },
    {
      key: 'provider',
      title: '服务提供商',
      dataIndex: 'provider',
      width: 120,
      sortable: true
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (value) => {
        const statusColors = {
          'active': 'bg-green-100 text-green-800',
          'inactive': 'bg-gray-100 text-gray-800',
          'pending': 'bg-yellow-100 text-yellow-800',
          'error': 'bg-red-100 text-red-800'
        };
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            statusColors[value as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
          }`}>
            {value}
          </span>
        );
      }
    },
    {
      key: 'serviceActivated',
      title: '服务激活',
      dataIndex: 'serviceActivated',
      width: 100,
      align: 'center',
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {value ? '已激活' : '未激活'}
        </span>
      )
    },
    {
      key: 'amount',
      title: '金额',
      dataIndex: 'amount',
      width: 120,
      align: 'right',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value?.toLocaleString() || 0}</span>
      )
    },
    {
      key: 'duration',
      title: '持续时间',
      dataIndex: 'duration',
      width: 100,
      align: 'right',
      render: (value) => (
        <span>{value ? `${value}天` : '-'}</span>
      )
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {value ? new Date(value * 1000).toLocaleString() : '-'}
        </span>
      )
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0 // 重置到第一页
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleRowClick = (record: UserService) => {
    setSelectedRecord(record);
    setShowDetailModal(true);
  };

  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setSelectedRecord(null);
  };

  const detailFields: DetailField[] = [
    { key: '_id', label: 'ID', type: 'id', copyable: true },
    { key: 'serviceID', label: '服务ID', type: 'id', copyable: true },
    { key: 'address', label: '用户地址', type: 'address', copyable: true },
    { key: 'provider', label: '服务提供商', type: 'text' },
    { key: 'providerAddress', label: '提供商地址', type: 'address', copyable: true },
    { key: 'status', label: '状态', type: 'text' },
    { key: 'serviceActivated', label: '服务已激活', type: 'boolean' },
    { key: 'duration', label: '持续时间（秒）', type: 'number' },
    { key: 'amount', label: '金额', type: 'amount' },
    { key: 'service', label: '服务名称', type: 'text' },
    { key: 'createdAt', label: '创建时间', type: 'timestamp' },
    { key: 'updatedAt', label: '更新时间', type: 'timestamp' },
    { key: 'endAt', label: '结束时间', type: 'timestamp' },
    { key: 'deletedAt', label: '删除时间', type: 'timestamp' }
  ];

  const handleSearch = () => {
    const searchParams: Partial<UserServiceQueryParams> = {
      ...filters,
      offset: 0 // 重置到第一页
    };

    if (searchText.trim()) {
      // 根据搜索文本类型判断搜索字段
      if (searchText.startsWith('0x') || searchText.length === 42) {
        searchParams.address = searchText;
      } else {
        searchParams.serviceID = searchText;
      }
    }

    updateParams(searchParams);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleClearFilters = () => {
    setSearchText('');
    setFilters({});
    updateParams({
      offset: 0,
      limit: 20,
      sortBy: 'createdAt',
      sortDesc: true
    });
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">用户服务</h1>
          <p className="text-gray-600 mt-1">管理和查看用户服务记录</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'serviceID', title: '服务ID' },
              { key: 'address', title: '用户地址' },
              { key: 'provider', title: '服务提供商' },
              { key: 'providerAddress', title: '提供商地址' },
              { key: 'status', title: '状态' },
              { key: 'serviceActivated', title: '服务已激活', render: (value) => formatBoolean(value) },
              { key: 'duration', title: '持续时间（秒）' },
              { key: 'amount', title: '金额' },
              { key: 'service', title: '服务名称' },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) },
              { key: 'endAt', title: '结束时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="user_services"
            disabled={loading || data.length === 0}
          />
          <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            新建
          </button>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索服务ID、用户地址..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <button
            onClick={handleSearch}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <Search className="h-4 w-4 mr-2" />
            搜索
          </button>
          <button
            onClick={handleClearFilters}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            清除筛选
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">加载数据时出错</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={handleRefresh}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  重试
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 数据表格 */}
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          onChange: handlePageChange
        }}
        sortConfig={{
          key: params.sortBy || 'createdAt',
          direction: params.sortDesc ? 'desc' : 'asc'
        }}
        onSort={handleSort}
        rowKey="_id"
        onRowClick={(record) => {
          console.log('点击行:', record);
        }}
        emptyText={error ? '加载失败' : '暂无数据'}
      />
    </div>
  );
};
