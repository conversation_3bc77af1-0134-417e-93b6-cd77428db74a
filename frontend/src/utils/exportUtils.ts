// 数据导出工具类

export interface ExportColumn {
  key: string;
  title: string;
  render?: (value: any, record: any) => string;
}

export interface ExportOptions {
  filename?: string;
  columns?: ExportColumn[];
  data: any[];
}

/**
 * 将数据导出为CSV格式
 */
export function exportToCSV(options: ExportOptions): void {
  const { filename = 'export', columns, data } = options;
  
  if (!data || data.length === 0) {
    alert('没有数据可导出');
    return;
  }

  // 如果没有指定列，使用数据的所有键
  const exportColumns = columns || Object.keys(data[0]).map(key => ({
    key,
    title: key
  }));

  // 创建CSV内容
  const headers = exportColumns.map(col => `"${col.title}"`).join(',');
  const rows = data.map(record => {
    return exportColumns.map(col => {
      let value = record[col.key];
      
      // 如果有自定义渲染函数，使用它
      if (col.render) {
        value = col.render(value, record);
      } else {
        // 处理特殊数据类型
        if (value === null || value === undefined) {
          value = '';
        } else if (typeof value === 'object') {
          value = JSON.stringify(value);
        } else if (typeof value === 'boolean') {
          value = value ? '是' : '否';
        } else if (typeof value === 'number' && value > 1000000000) {
          // 可能是时间戳，转换为日期
          value = new Date(value * 1000).toLocaleString();
        }
      }
      
      // 转义CSV特殊字符
      return `"${String(value).replace(/"/g, '""')}"`;
    }).join(',');
  });

  const csvContent = [headers, ...rows].join('\n');
  
  // 添加BOM以支持中文
  const BOM = '\uFEFF';
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
  
  downloadFile(blob, `${filename}.csv`);
}

/**
 * 将数据导出为JSON格式
 */
export function exportToJSON(options: ExportOptions): void {
  const { filename = 'export', columns, data } = options;
  
  if (!data || data.length === 0) {
    alert('没有数据可导出');
    return;
  }

  let exportData = data;

  // 如果指定了列，只导出指定的列
  if (columns) {
    exportData = data.map(record => {
      const filteredRecord: any = {};
      columns.forEach(col => {
        let value = record[col.key];
        
        if (col.render) {
          value = col.render(value, record);
        }
        
        filteredRecord[col.title] = value;
      });
      return filteredRecord;
    });
  }

  const jsonContent = JSON.stringify(exportData, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  
  downloadFile(blob, `${filename}.json`);
}

/**
 * 下载文件
 */
function downloadFile(blob: Blob, filename: string): void {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // 清理URL对象
  window.URL.revokeObjectURL(url);
}

/**
 * 格式化时间戳为可读日期
 */
export function formatTimestamp(timestamp: number): string {
  if (!timestamp || timestamp === 0) return '-';
  return new Date(timestamp * 1000).toLocaleString();
}

/**
 * 格式化布尔值
 */
export function formatBoolean(value: boolean): string {
  return value ? '是' : '否';
}

/**
 * 格式化对象为字符串
 */
export function formatObject(obj: any): string {
  if (!obj) return '-';
  if (typeof obj === 'object') {
    return JSON.stringify(obj);
  }
  return String(obj);
}

/**
 * 截断长文本
 */
export function truncateText(text: string, maxLength: number = 50): string {
  if (!text) return '-';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}
