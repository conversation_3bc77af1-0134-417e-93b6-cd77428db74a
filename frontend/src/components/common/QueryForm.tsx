import React, { useState } from 'react';
import { clsx } from 'clsx';
import { Search, Filter, X, Calendar, Hash, User, Building } from 'lucide-react';

export interface FormField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'dateRange' | 'number' | 'boolean';
  placeholder?: string;
  options?: { label: string; value: string | number | boolean }[];
  defaultValue?: any;
  validation?: (value: any) => string | null;
}

export interface QueryFormProps {
  fields: FormField[];
  onSubmit: (values: Record<string, any>) => void;
  onReset?: () => void;
  loading?: boolean;
  className?: string;
  showAdvanced?: boolean;
}

export const QueryForm: React.FC<QueryFormProps> = ({
  fields,
  onSubmit,
  onReset,
  loading = false,
  className,
  showAdvanced = false
}) => {
  const [values, setValues] = useState<Record<string, any>>(() => {
    const initialValues: Record<string, any> = {};
    fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        initialValues[field.key] = field.defaultValue;
      }
    });
    return initialValues;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(showAdvanced);

  const handleChange = (key: string, value: any) => {
    setValues(prev => ({ ...prev, [key]: value }));
    
    // 清除该字段的错误
    if (errors[key]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    fields.forEach(field => {
      if (field.validation) {
        const error = field.validation(values[field.key]);
        if (error) {
          newErrors[field.key] = error;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // 过滤掉空值
      const filteredValues = Object.entries(values).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);
      
      onSubmit(filteredValues);
    }
  };

  const handleReset = () => {
    const resetValues: Record<string, any> = {};
    fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        resetValues[field.key] = field.defaultValue;
      }
    });
    setValues(resetValues);
    setErrors({});
    onReset?.();
  };

  const renderField = (field: FormField) => {
    const value = values[field.key] || '';
    const error = errors[field.key];
    const hasError = !!error;

    const baseInputClasses = clsx(
      'block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm',
      hasError
        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
        : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
    );

    const getFieldIcon = () => {
      switch (field.type) {
        case 'text':
          if (field.key.includes('address')) return <User className="h-4 w-4" />;
          if (field.key.includes('provider')) return <Building className="h-4 w-4" />;
          return <Hash className="h-4 w-4" />;
        case 'date':
        case 'dateRange':
          return <Calendar className="h-4 w-4" />;
        case 'number':
          return <Hash className="h-4 w-4" />;
        default:
          return <Search className="h-4 w-4" />;
      }
    };

    switch (field.type) {
      case 'text':
      case 'number':
        return (
          <div key={field.key} className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {field.label}
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                {getFieldIcon()}
              </div>
              <input
                type={field.type}
                value={value}
                onChange={(e) => handleChange(field.key, e.target.value)}
                placeholder={field.placeholder}
                className={clsx(baseInputClasses, 'pl-10')}
              />
            </div>
            {hasError && (
              <p className="mt-1 text-sm text-red-600">{error}</p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={field.key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {field.label}
            </label>
            <select
              value={value}
              onChange={(e) => handleChange(field.key, e.target.value)}
              className={baseInputClasses}
            >
              <option value="">请选择...</option>
              {field.options?.map(option => (
                <option key={String(option.value)} value={String(option.value)}>
                  {option.label}
                </option>
              ))}
            </select>
            {hasError && (
              <p className="mt-1 text-sm text-red-600">{error}</p>
            )}
          </div>
        );

      case 'boolean':
        return (
          <div key={field.key}>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={value || false}
                onChange={(e) => handleChange(field.key, e.target.checked)}
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span className="ml-2 text-sm font-medium text-gray-700">
                {field.label}
              </span>
            </label>
            {hasError && (
              <p className="mt-1 text-sm text-red-600">{error}</p>
            )}
          </div>
        );

      case 'date':
        return (
          <div key={field.key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {field.label}
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                <Calendar className="h-4 w-4" />
              </div>
              <input
                type="date"
                value={value}
                onChange={(e) => handleChange(field.key, e.target.value)}
                className={clsx(baseInputClasses, 'pl-10')}
              />
            </div>
            {hasError && (
              <p className="mt-1 text-sm text-red-600">{error}</p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  // 将字段分为基础和高级
  const basicFields = fields.slice(0, 3);
  const advancedFields = fields.slice(3);

  return (
    <div className={clsx('bg-white rounded-lg shadow p-6', className)}>
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* 基础字段 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {basicFields.map(renderField)}
        </div>

        {/* 高级字段 */}
        {advancedFields.length > 0 && (
          <>
            <div className="flex items-center justify-between pt-2">
              <button
                type="button"
                onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
                className="flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                <Filter className="h-4 w-4 mr-1" />
                高级筛选
                {isAdvancedOpen ? (
                  <X className="h-4 w-4 ml-1" />
                ) : (
                  <span className="ml-1">({advancedFields.length})</span>
                )}
              </button>
            </div>

            {isAdvancedOpen && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                {advancedFields.map(renderField)}
              </div>
            )}
          </>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={handleReset}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <X className="h-4 w-4 mr-2" />
            重置
          </button>

          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            <Search className={clsx('h-4 w-4 mr-2', loading && 'animate-spin')} />
            {loading ? '查询中...' : '查询'}
          </button>
        </div>
      </form>
    </div>
  );
};
