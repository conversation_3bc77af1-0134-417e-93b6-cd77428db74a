// Package main demonstrates comprehensive usage of the VGraph RPC client.
// This example shows how to connect to VGraph nodes, create and sign transactions,
// submit them to the network, and query blockchain state using both TCP and WebSocket transports.
//
// The example covers:
//   - Connecting to VGraph nodes via TCP and WebSocket
//   - Setting up wallets and importing keypairs
//   - Building and signing smart contract transactions
//   - Submitting transactions to the mempool
//   - Retrieving transaction receipts and confirmation status
//   - Querying the latest block information
//   - Making read-only contract function calls
//   - Using the low-level Call method for custom RPC operations
//
// This serves as a complete reference for developers building applications
// that need to interact with the VGraph blockchain network, demonstrating
// the full transaction lifecycle from creation to confirmation.
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/virtualeconomy/go-vgraph/examples/jsonutil/jsonutil"
	"github.com/virtualeconomy/go-vgraph/rpc"
	"github.com/virtualeconomy/go-vgraph/transaction"
	"github.com/virtualeconomy/go-vgraph/utils"
	"github.com/virtualeconomy/go-vgraph/wallet"
)

var orders = `
[{
  "_id": "6851359d6e8b76db007df830",
  "createdAt": **********,
  "updatedAt": **********,
  "type": "OrderTypePurchase",
  "amount": 0.005,
  "amountPaid": 0,
  "provider": "v-kube-service",
  "address": "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR",
  "recipient": "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr",
  "status": "OrderPending",
  "lastPaymentTS": 0,
  "paidTS": 0,
  "filedTS": 0,
  "publicKey": "GvaJxsN4wzfxhM3v8iAA9b1u6sCi4ahtjTRaZ4SbiJ9T",
  "userServiceIDs": [
    "6851359d6e8b76db007df831"
  ],
  "items": [
    {
      "userServiceID": "6851359d6e8b76db007df831",
      "duration": 2,
      "amount": 0.005
    }
  ]
}]
`

var filter = `
{
  "address": "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR",
  "limit": 10,
  "offset": 0
}

`

func main() {
	// Define connection URLs
	tcpURL := "682afc8eecab37cdeffac5f7.europe.test.vkube.vcloud.systems:9877"
	wsURL := "wss://682afc8eecab37cdeffac5f7.europe.test.vkube.vcloud.systems"

	fmt.Println("Connecting to VGraph nodes...")
	// Connect to TCP and WebSocket clients
	tcpClient, err := rpc.Connect(tcpURL)
	if err != nil {
		log.Fatalf("Failed to connect to TCP endpoint: %v", err)
	}
	defer tcpClient.Close()

	wsClient, err := rpc.Connect(wsURL)
	if err != nil {
		log.Fatalf("Failed to connect to WebSocket endpoint: %v", err)
	}
	defer wsClient.Close()

	fmt.Println("Successfully connected.")

	// 1. Set up wallet and keypair
	fmt.Println("\n=== 1. Setting up wallet ===")
	w := wallet.NewWallet()
	// Use the same private key as the python example for consistency
	privateKeyHex := "0x0cf9455dbbb7b26ff02fe76d25f228b0eee8d077f8d6ea786c0cefbd2a91480731729d4ec734c5198f72c9ccda9e0bd62ed512ee3e8b526b35d6db147d9d12a5"
	privateKeyBytes, err := utils.HexToBytes(privateKeyHex)
	if err != nil {
		log.Fatalf("Failed to decode private key: %v", err)
	}

	keypair, err := w.ImportKeypair("main", privateKeyBytes)
	if err != nil {
		log.Fatalf("Failed to import keypair: %v", err)
	}
	w.SetDefaultKeypair("main")

	addressBytes := keypair.Address()
	addressHex := utils.BytesToHex(addressBytes)
	fmt.Printf("Wallet setup complete. Address: %s\n", addressHex)

	// 2. Build and sign a transaction
	fmt.Println("\n=== 2. Building and Signing Transaction ===")
	builder := transaction.NewTransactionBuilder().WithSender(addressHex)
	contractAddress := "0xab6492e2c4bf3d33f2805b164b130fed1f97bff0fb5b30f3e2240979268bd553"
	tx := builder.BuildExecuteContractTx(
		contractAddress,
		"insert_many",
		[]interface{}{"order", orders},
		100000,
	)

	// Timestamps must be in nanoseconds
	ts := uint64(time.Now().UnixNano())
	err = w.SignTransaction(tx, nil, &ts) // Use nil to select default keypair
	if err != nil {
		log.Fatalf("Failed to sign transaction: %v", err)
	}

	txJSON, err := tx.ToJSON()
	if err != nil {
		log.Fatalf("Failed to serialize transaction to JSON: %v", err)
	}
	fmt.Println("Transaction built and signed.")

	// 3. Send the transaction
	fmt.Println("\n=== 3. Sending Transaction ===")
	result, err := tcpClient.SendRawTransaction(context.Background(), txJSON)
	if err != nil {
		log.Fatalf("Failed to send transaction: %v", err)
	}
	fmt.Println("Transaction sent successfully. Result: ", result)

	txHashBytes, err := tx.Hash()
	if err != nil {
		log.Fatalf("Failed to calculate transaction hash: %v", err)
	}
	txHashHex := utils.BytesToHex(txHashBytes)
	fmt.Printf("Transaction sent successfully. Hash: %s\n", txHashHex)

	// 4. Get transaction receipt
	fmt.Println("\n=== 4. Fetching Transaction Receipt ===")
	fmt.Println("Waiting 15 seconds for transaction to be confirmed...")
	time.Sleep(15 * time.Second)

	receipt, err := tcpClient.GetTransactionReceipt(context.Background(), txHashHex)
	if err != nil {
		log.Fatalf("Failed to get transaction receipt: %v", err)
	}
	fmt.Println("Receipt received:")
	fmt.Println(jsonutil.PrettyPrint(string(receipt)))

	// 5. Get best block information
	fmt.Println("\n=== 5. Fetching Best Block (via WebSocket) ===")
	bestBlock, err := wsClient.GetBestBlock(context.Background())
	if err != nil {
		log.Fatalf("Failed to get best block: %v", err)
	}
	fmt.Println("Best block info:")
	fmt.Println(jsonutil.PrettyPrint(string(bestBlock)))

	// Query a contract
	fmt.Println("\n=== Querying Contract ===")
	queryParams := rpc.QueryContractParams{
		ContractAddress: contractAddress,
		FunctionName:    "find",
		Args:            []interface{}{"order", filter},
	}
	queryResult, err := tcpClient.QueryContract(context.Background(), queryParams)
	if err != nil {
		log.Fatalf("Failed to query contract: %v", err)
	}
	fmt.Println("Contract query result:")
	fmt.Println(jsonutil.PrettyPrint(string(queryResult)))
}
